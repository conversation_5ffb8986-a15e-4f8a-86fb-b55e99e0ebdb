<?xml version="1.0" encoding="UTF-8"?>
<CompositeView
    id="id_rootview"
    layout="{layout.container}">
    <WebView
        id="id_webview"/>
    <CompositeView
        id="id_network_container"
        layout="{layout.network_container}"
        background="{color.PAGE_BG_COLOR}"
        visibility="{enum.View.Visibility.None}">
        <CompositeView
            id="id_network_error"
            width="{sdp(300)}"
            height="{sdp(255)}"
            layout="{layout.network_error}">
            <ImageView
                id="id_error_icon"
                width="{sdp(140)}"
                height="{sdp(140)}"
                src="{img(images/ic_no_wifi.png)}"
                scaleType="{enum.ImageView.ScaleType.Center}"/>
            <TextView
                id="id_error_text"
                text="{string.NETWORK_ERROR}"
                propertySetName="extend/hdt/FontBody2"
                color="{theme.color.White_1}"/>
            <ButtonBM
                id="id_error_btn"
                text="{string.RELOAD}"
                height="{sdp(50)}"/>
        </CompositeView>
    </CompositeView>
</CompositeView>
