/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"presenter/HomePresenter.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/page/PageLink.js","name":""},{"path":"/usr/framework/yunos/appmodel/Presenter.js","name":""},{"path":"/usr/framework/yunos/ui/view/CompositeView.js","name":""},{"path":"/usr/framework/yunos/ui/view/WebView.js","name":""},{"path":"/usr/framework/yunos/ui/view/View.js","name":""},{"path":"../bridge/ContainerBridge.js","name":""},{"path":"../utils/log.js","name":""},{"path":"../utils/UserTrackHelper.js","name":""},{"path":"../service/NetworkService.js","name":""},{"path":"../utils/Utils.js","name":""},{"path":"../Consts.js","name":""},{"cname":"HomePresenter","supers":{"ec":8},"fields":{"webview":20,"networkContainer":21,"webPageLoadTimer":0,"webPageLoaded":2,"isDestroyed":2,"onWebPageStatusChanged":0},"methods":[19,22,28,29,31,32,33,34,35,36,38,39,40]},{"fname":"constructor","ret":18},{"instance_of":10},{"instance_of":9},{"fname":"get events","ret":27},{"fname":""},{"oname":"","fields":{"tap":-23}},{"fname":""},{"oname":"","fields":{"tap":-25}},{"oname":"","fields":{"id_webview":-24,"id_error_btn":-26}},{"fname":"onCreate"},{"fname":"onViewAttached","params":{"tc":30}},{"instance_of":11},{"fname":"initListener","private":1},{"fname":"checkWebPageStatus","private":1},{"fname":"removeTimeout","private":1,"params":{"tc":0}},{"fname":"reload","private":1},{"fname":"checkNetwork","private":1},{"fname":"onPageLink","params":{"tc":37}},{"instance_of":7},{"fname":"onPageShow"},{"fname":"onPageHide"},{"fname":"onDestroy"},{"fname":"","params":{"tc":2}},{"fname":""},{"oname":"","fields":{"from":-3}}],"exports":19}*/"use strict";const /*<@8>*/ec = require("/usr/framework/yunos/appmodel/Presenter.js");const /*<@11>*/hc = require("/usr/framework/yunos/ui/view/View.js");const /*<@12>*/ic = require("/opt/app/bilibili.alios.cn/src/bridge/ContainerBridge.js");const /*<@13>*/jc = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@14>*/kc = require("/opt/app/bilibili.alios.cn/src/utils/UserTrackHelper.js");const /*<@15>*/lc = require("/opt/app/bilibili.alios.cn/src/service/NetworkService.js");const /*<@16>*/mc = require("/opt/app/bilibili.alios.cn/src/utils/Utils.js");const /*<@17>*/nc = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@3>*/oc = "HomePresenter";class /*<@19>*/HomePresenter extends ec{get /*<@22>*/events(){return /*<@27>*/{id_webview:/*<@24>*/{tap:/*<@23>*/() =>{jc.D(oc, "webview tap");}},id_error_btn:/*<@26>*/{tap:/*<@25>*/() =>{this.reload();}}};}/*<@28>*/onCreate(){jc.I(oc, "onCreate");this.isDestroyed=false;this.attachView("home");}/*<@29>*/onViewAttached(/*<@30>*/tc){jc.I(oc, "onViewAttached");let uc = tc.findViewById("id_webview");uc.settings.errorPage="../../assets/error.html";uc.settings.longPressSelectionControllerEnabled=false;uc.background=mc.getColor("PAGE_BG_COLOR");uc.url=nc.HOME_URL;if(uc.setAudioSessionManagerDisabled&&typeof uc.setAudioSessionManagerDisabled==="function") {uc.setAudioSessionManagerDisabled(true);}ic.getInstance().init(/*<@21>*/tc);ic.getInstance().setWebView(uc);this.webview=uc;this.networkContainer=tc.findViewById("id_network_container");this.initListener();}/*<@31>*/initListener(){this.webPageLoaded=false;this.checkWebPageStatus();this.onWebPageStatusChanged=/*<@41>*/(/*<@2>*/tc) =>{if(this.isDestroyed) {return;}jc.I(oc, "onWebPageStatusChanged, loaded:", tc);this.webPageLoaded=true;};ic.getInstance().on(nc.EV_WEBPAGE_STATUS_CHANGED, this.onWebPageStatusChanged);}/*<@32>*/checkWebPageStatus(){this.removeTimeout(this.webPageLoadTimer);this.webPageLoadTimer=setTimeout(/*<@42>*/() =>{if(this.webPageLoaded) {this.networkContainer.visibility=hc.Visibility.None;}else {this.networkContainer.visibility=hc.Visibility.Visible;}this.removeTimeout(this.webPageLoadTimer);}, nc.WEBPAGE_LOAD_TIMEOUT);}/*<@33>*/removeTimeout(tc){if(tc) {clearTimeout(tc);tc=null;}}/*<@34>*/reload(){let /*<@1>*/tc = lc.getInstance().getNetworkConnState();if(tc!==1) {this.networkContainer.visibility=hc.Visibility.Visible;}else {this.networkContainer.visibility=hc.Visibility.None;if(this.webview) {this.webview.reload();}}}/*<@35>*/checkNetwork(){let /*<@1>*/tc = lc.getInstance().getNetworkConnState();jc.I(oc, "checkNetwork,", tc, this.webPageLoaded);if(tc===1) {this.networkContainer.visibility=hc.Visibility.None;if(!this.webPageLoaded) {this.webview.url=nc.HOME_URL;}}else {this.networkContainer.visibility=hc.Visibility.Visible;}}/*<@36>*/onPageLink(/*<@37>*/tc){jc.D(oc, "onPageLink, data:", tc.data);let /*<@3>*/uc = "applist";let /*<@3>*/vc = tc.data;if(vc) {try {let wc = JSON.parse(tc.data);if(wc) {if(wc.command==="voice") {uc="voice";}else {if(wc.otype&&wc.oid) {this.webview.url=nc.HOME_URL+"?play="+wc.otype+"_"+wc.oid;}}}}catch (wc) {jc.E(oc, "onPageLink failed", wc);}}kc.getInstance().sendEvent("main_page", "bilibili_app_start", /*<@43>*/{from:uc});}/*<@38>*/onPageShow(){jc.I(oc, "onPageShow");this.checkNetwork();kc.getInstance().enterPage("main_page");}/*<@39>*/onPageHide(){jc.I(oc, "onPageHide");kc.getInstance().leavePage("main_page");}/*<@40>*/onDestroy(){jc.D(oc, "onDestroy");this.isDestroyed=true;this.removeTimeout(this.webPageLoadTimer);ic.getInstance().off(nc.EV_WEBPAGE_STATUS_CHANGED, this.onWebPageStatusChanged);ic.releaseInstance();this.webview.destroy();}}module.exports=HomePresenter;
