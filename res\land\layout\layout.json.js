/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
"use strict";module.exports={"res/land/layout":function(){const t=require("/usr/framework/yunos/device/Screen.js");const e=t.getInstance();var r={};var n={};function SDP(t){return r[t]||(r[t]=e.getPixelBySDp(t))}function DP(t){return n[t]||(n[t]=e.getPixelByDp(t))}return{container:function(t){const e=require("/usr/framework/yunos/ui/layout/RelativeLayout.js");let r=new e;return r.setLayoutParam("id_webview","align",{left:"parent",top:"parent",right:"parent",bottom:"parent"}),r.setLayoutParam("id_network_container","align",{left:"parent",top:"parent",right:"parent",bottom:"parent"}),r.setLayoutParam("videoLimitView","align",{left:"parent",top:"parent",right:"parent",bottom:"parent"}),r},network_container:function(t){const e=require("/usr/framework/yunos/ui/layout/RelativeLayout.js");let r=new e;return r.setLayoutParam(0,"align",{center:"parent",middle:"parent"}),r},network_error:function(t){const e=require("/usr/framework/yunos/ui/layout/RelativeLayout.js");let r=new e;return r.setLayoutParam("id_error_icon","align",{center:"parent",top:"parent"}),r.setLayoutParam("id_error_text","align",{center:"parent",top:{target:"id_error_icon",side:"bottom"}}),r.setLayoutParam("id_error_text","margin",{top:SDP(15)}),r.setLayoutParam("id_error_btn","align",{left:"parent",right:"parent",bottom:"parent"}),r},__valid_compiled__:true}}};