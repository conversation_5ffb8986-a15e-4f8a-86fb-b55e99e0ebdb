/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"index.js"},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/hdt/page/BMPage.js","name":""},{"path":"/usr/framework/yunos/page/Page.js","name":""},{"path":"/usr/framework/hdt/utils/RouterHelper.js","name":"RouterHelper"},{"path":"/usr/framework/hdt/utils/RouterHelper.js","name":""},{"path":"/usr/framework/yunos/appmodel/StackRouter.js","name":""},{"path":"/usr/framework/yunos/ui/view/CompositeView.js","name":""},{"path":"/usr/framework/yunos/ui/view/ImageView.js","name":""},{"path":"/usr/framework/yunos/ui/view/View.js","name":""},{"path":"/usr/framework/yunos/page/PageLink.js","name":""},{"path":"/usr/framework/yunos/content/resource/Resource.js","name":""},{"path":"Consts.js","name":""},{"instance_of":17},{"path":"utils/log.js","name":""},{"path":"utils/Utils.js","name":""},{"iname":"IPage","fields":{"pageapi":22}},{"oname":"","fields":{"mainWindow":-23}},{"oname":"","fields":{"stopFastRender":0}},{"cname":"App","supers":{"p":7},"fields":{"stackRouter":26,"closeImage":27},"methods":[25,28,29,34,35,36,37,38,40,41,42,44,45,46,47,48,49]},{"fname":"constructor","ret":24},{"instance_of":11},{"instance_of":13},{"fname":"useRouter","ret":2},{"fname":"getWindowConfig","ret":33},{"aname":"","element":3,"mode":"normal"},{"oname":"","fields":{"top":-5,"left":-5,"width":-1,"height":-1,"opaque":-2,"extraSwitches":-30,"requireAdjust":-2}},{"oname":"","fields":{"opaque":-2,"extraSwitches":-30}},{"uname":"","types":[31,32]},{"fname":"initPageHeader"},{"fname":"onCreate","hobj":1},{"fname":"onStart","hobj":1},{"fname":"onFirstFrameCompleted"},{"fname":"initRouter","private":1,"params":{"tc":39}},{"instance_of":14},{"fname":"showCloseImage"},{"fname":"hideCloseImage"},{"fname":"onLink","params":{"tc":43}},{"instance_of":15},{"fname":"onActivate","hobj":1},{"fname":"onDeactivate","hobj":1},{"fname":"onShow","hobj":1},{"fname":"onHide","hobj":1},{"fname":"onStop","hobj":1},{"fname":"onDestroy","hobj":1},{"oname":"","fields":{"top":-5}},{"bname":"Object"},{"instance_of":12},{"fname":"","params":{"vc":3},"ret":0},{"instance_of":8},{"oname":"","fields":{"launchMode":-3}},{"fname":""}],"exports":25}*/"use strict";process.env.AGIL_ENABLE_BOUNDBOX="1";const /*<@7>*/p = require("/usr/framework/hdt/page/BMPage.js");const /*<@10>*/RouterHelper_1 = require("/usr/framework/hdt/utils/RouterHelper.js");const /*<@11>*/r = require("/usr/framework/yunos/appmodel/StackRouter.js");const /*<@13>*/t = require("/usr/framework/yunos/ui/view/ImageView.js");const /*<@14>*/u = require("/usr/framework/yunos/ui/view/View.js");const /*<@16>*/w = require("/usr/framework/yunos/content/resource/Resource.js");const x = require("/usr/framework/yunos/util/Trace.js");const /*<@17>*/y = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@18>*/z = y.RoutePath;const /*<@19>*/A = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@20>*/B = require("/opt/app/bilibili.alios.cn/src/utils/Utils.js");const /*<@3>*/C = "Main";const /*<@5>*/D = 120;const /*<@5>*/E = 0;const /*<@1>*/F = B.getScreenWidth()-D;const /*<@1>*/G = B.getScreenHeight()-E;const /*<@1>*/H = B.getDimen("SYSTEM_CLOSE_IMAGE_WIDTH");const /*<@1>*/I = B.getDimen("SYSTEM_CLOSE_IMAGE_HEIGHT");class /*<@25>*/App extends p{/*<@28>*/useRouter(){return false;}/*<@29>*/getWindowConfig(){if(this.isLand) {return /*<@31>*/{top:0,left:D,width:F,height:G,opaque:false,extraSwitches:/*<@30>*/["--fast-render-mode=true"],requireAdjust:false};}else {return /*<@32>*/{opaque:false,extraSwitches:/*<@30>*/["--fast-render-mode=true"]};}}/*<@34>*/initPageHeader(){}/*<@35>*/onCreate(){x.beginTrace(x.Tag.VIEW, "Bilibili_App_onCreate");A.I(C, "onCreate");super.onCreate();x.endTrace(x.Tag.VIEW);}/*<@36>*/onStart(){x.beginTrace(x.Tag.VIEW, "Bilibili_App_onStart");super.onStart();if(this.isLand) {this.window.hideStatusBar();}this.window.background=B.getColor("PAGE_BG_COLOR");this.rootContainer.left=0;this.rootContainer.top=0;this.rootContainer.width=this.window.width;this.rootContainer.height=this.window.height;this.initRouter(this.rootContainer);this.showCloseImage();if(this.rootContainer.layout) {const tc = this.rootContainer.layout.layoutParams[0];if(tc) {A.D(C, "onStart, rootContainerLayoutParams.marginTop:", tc.marginTop);if(tc.marginTop>0) {this.rootContainer.layout.setLayoutParam(0, "margin", /*<@50>*/{top:0,});}}}if(typeof this.stopFastRender==="function") {this.stopFastRender();}else {if(/*<@51>*/this.pageapi) {/*<@51>*/this.pageapi.mainWindow.stopFastRender();}}x.endTrace(x.Tag.VIEW);}/*<@37>*/onFirstFrameCompleted(){A.I(C, "TraceInfo-StartUp-BilibiliIndex-FirstFrameCompleted");}/*<@38>*/initRouter(/*<@39>*/tc){this.stackRouter=new r();this.stackRouter.container=/*<@52>*/tc;this.stackRouter.config.presenterSearchPath="/src/presenter";const uc = new RouterHelper_1.RouterHelper(this.stackRouter);this.stackRouter.config.presenterLoader=/*<@53>*/(/*<@3>*/vc) => uc.getPresenterPath(vc);this.stackRouter.run(/*<@51>*/this);this.stackRouter.navigate(z.HOME, this.sourceLink.data, /*<@55>*/{launchMode:"single"});}/*<@40>*/showCloseImage(){if(!this.closeImage) {let tc = new t();tc.src=w.getInstance().getImageSrc("images/ic_system_close.png");tc.width=H;tc.height=I;tc.scaleType=t.ScaleType.Fitxy;this.window.addChild(tc);this.closeImage=tc;this.closeImage.left=this.isLand ? 0 : this.window.width-H;B.setOnTapListener(tc, /*<@56>*/() =>{A.I(C, "onTap closeImage");this.hidePage();});}this.closeImage.visibility=u.Visibility.Visible;}/*<@41>*/hideCloseImage(){if(this.closeImage) {this.closeImage.visibility=u.Visibility.None;}}/*<@42>*/onLink(/*<@43>*/tc){A.D(C, "onLink", tc.data);this.stackRouter.navigate(z.HOME, tc.data, /*<@55>*/{launchMode:"single"});}/*<@44>*/onActivate(){A.I(C, "onActivate");super.onActivate();let tc = require("/opt/app/bilibili.alios.cn/src/bridge/ContainerBridge.js");tc.getInstance().notifyAppEvent("activate");}/*<@45>*/onDeactivate(){A.I(C, "onDeactivate");super.onDeactivate();let tc = require("/opt/app/bilibili.alios.cn/src/bridge/ContainerBridge.js");tc.getInstance().notifyAppEvent("deactivate");}/*<@46>*/onShow(){A.I(C, "onShow");super.onShow();let tc = require("/opt/app/bilibili.alios.cn/src/bridge/ContainerBridge.js");tc.getInstance().notifyAppEvent("show");}/*<@47>*/onHide(){A.I(C, "onHide");super.onHide();let tc = require("/opt/app/bilibili.alios.cn/src/bridge/ContainerBridge.js");tc.getInstance().notifyAppEvent("hide");}/*<@48>*/onStop(){A.I(C, "onStop");super.onStop();B.setOnTapListener(this.closeImage, null);let tc = require("/opt/app/bilibili.alios.cn/src/bridge/ContainerBridge.js");tc.getInstance().notifyAppEvent("close");this.stackRouter.destroy();this.stackRouter=null;}/*<@49>*/onDestroy(){A.I(C, "onDestroy");super.onDestroy();}}module.exports=App;
