/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"utils/TrafficHelper.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/core/EventEmitter.js","name":""},{"path":"/usr/framework/yunos/page/PageLink.js","name":""},{"path":"/usr/framework/yunos/ui/widget/AlertDialog.js","name":""},{"path":"/usr/framework/yunos/ui/view/CompositeView.js","name":""},{"path":"/usr/framework/yunos/ui/view/TextView.js","name":""},{"path":"/usr/framework/yunos/content/resource/Resource.js","name":""},{"path":"/usr/framework/yunos/content/Settings.js","name":""},{"path":"/usr/framework/yunos/provider/DataResolver.js","name":""},{"path":"/usr/framework/yunos/provider/DataObserver.js","name":""},{"path":"/usr/framework/yunos/net/NetQuery.js","name":""},{"path":"log.js","name":""},{"path":"../service/NetworkService.js","name":""},{"path":"../Consts.js","name":""},{"path":"../index.js","name":""},{"iname":"IAlertDialog","supers":{"Ha":9},"fields":{"type":1}},{"cname":"NetworkEnableChangeObserver","supers":{"Na":15},"fields":{"uri":-3,"callback":0},"methods":[23,24]},{"fname":"constructor","params":{"uri":3,"callback":0},"ret":22,"hobj":1},{"fname":"onChange","params":{"tc":3}},{"cname":"TrafficHelper","supers":{"Fa":7},"fields":{"dialog":21,"trafficType":-3,"netQuery":-27,"allNetworkDataEnableInitCompleted":-2,"allNetworkDataEnableValue":-1,"videoNetworkDataEnableInitCompleted":-2,"videoNetworkDataEnableValue":-1,"networkRemainingData":-1},"sfields":{"static instance":25,"const getInstance":28},"methods":[26,29,30,31,33,34,35,36,37,38,39,40,41,42]},{"fname":"constructor","ret":25,"hobj":1},{"instance_of":16},{"fname":"static getInstance","ret":25},{"fname":"initTrafficType","private":1,"params":{"tc":0}},{"fname":"initTrafficSwitch","private":1},{"fname":"getAllNetworkDataEnableValue","private":1,"params":{"tc":32}},{"instance_of":14},{"fname":"getVideoNetworkDataEnableValue","private":1,"params":{"tc":32}},{"fname":"handleNetworkDataEnableChanged","private":1},{"fname":"checkTrafficState","ret":2},{"fname":"checkRemainingData","private":1,"params":{"tc":2}},{"fname":"getExtendedRemainingTraffic","private":1,"params":{"tc":2}},{"fname":"showDialog","private":1,"params":{"tc":1}},{"fname":"setContentView","private":1,"params":{"tc":21,"uc":1}},{"fname":"closeDialog","private":1,"params":{"tc":2,"uc":1}},{"fname":"jumpToSetting","private":1},{"fname":"jumpToPersonalCenter","private":1},{"fname":"","params":{"uc":1,"vc":3}},{"fname":""},{"fname":""},{"fname":"","params":{"uc":1}},{"fname":"","params":{"uc":1}},{"fname":""},{"fname":"","params":{"uc":1,"vc":1}},{"fname":"","params":{"vc":51}},{"bname":"Object"},{"fname":""},{"oname":"","fields":{"text":-3,"style":-3}},{"oname":"","fields":{"text":-3}},{"aname":"","element":0,"mode":"normal"},{"instance_of":10},{"instance_of":11},{"fname":"","params":{"uc":51,"vc":51}},{"oname":"","fields":{"channelId":-3}},{"fname":"","params":{"vc":51,"wc":51}}],"exports":26}*/"use strict";const /*<@7>*/Fa = require("/usr/framework/yunos/core/EventEmitter.js");const /*<@8>*/Ga = require("/usr/framework/yunos/page/PageLink.js");const /*<@9>*/Ha = require("/usr/framework/yunos/ui/widget/AlertDialog.js");const /*<@11>*/Ja = require("/usr/framework/yunos/ui/view/TextView.js");const /*<@12>*/Ka = require("/usr/framework/yunos/content/resource/Resource.js");const /*<@13>*/La = require("/usr/framework/yunos/content/Settings.js");const /*<@14>*/Ma = require("/usr/framework/yunos/provider/DataResolver.js");const /*<@15>*/Na = require("/usr/framework/yunos/provider/DataObserver.js");const /*<@16>*/Oa = require("/usr/framework/yunos/net/NetQuery.js");const Pa = Oa.TrafficThreshold;const /*<@17>*/Qa = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@18>*/Ra = require("/opt/app/bilibili.alios.cn/src/service/NetworkService.js");const /*<@19>*/Sa = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@20>*/Ta = require("/opt/app/bilibili.alios.cn/src/index.js");const /*<@3>*/Ua = "TrafficHelper";const /*<@5>*/Va = 101;class /*<@23>*/NetworkEnableChangeObserver extends Na{constructor(/*<@3>*/uri, callback){Qa.I(Ua, "NetworkEnableChangeObserver constructor", uri);super(uri);this.uri=uri;this.callback=callback;}/*<@24>*/onChange(/*<@3>*/tc){Qa.I(Ua, "NetworkEnableChangeObserver onChange", tc);if(this.uri!==tc) {return;}if(this.callback) {this.callback();}}}class /*<@26>*/TrafficHelper extends Fa{constructor(){super();this.netQuery=new Oa();this.trafficType="";this.allNetworkDataEnableInitCompleted=false;this.allNetworkDataEnableValue=1;this.videoNetworkDataEnableInitCompleted=false;this.videoNetworkDataEnableValue=1;this.networkRemainingData=Va;this.initTrafficType();this.initTrafficSwitch();}static /*<@28>*/getInstance(){if(!this.instance) {this.instance=new TrafficHelper();}return this.instance;}/*<@29>*/initTrafficType(tc){this.netQuery.getExtendedTrafficType(Sa.PACKAGE_NAME, /*<@43>*/(/*<@1>*/uc, /*<@3>*/vc) =>{if(uc!==Oa.ErrorCode.SUCCESS) {Qa.I(Ua, "getExtendedTrafficType failed", uc);return;}Qa.I(Ua, "getExtendedTrafficType", vc);if(vc) {this.trafficType=vc;if(tc) {tc();}}});}/*<@30>*/initTrafficSwitch(){let tc = Ma.getInstance(Ta.getInstance());this.getAllNetworkDataEnableValue(tc);this.getVideoNetworkDataEnableValue(tc);let /*<@3>*/uc = La.Secure.getUriFor(La.Secure.NETWORK_DATA_ENABLE_ALL);let /*<@22>*/vc = new NetworkEnableChangeObserver(uc, /*<@44>*/() =>{this.getAllNetworkDataEnableValue(tc);});tc.registerObserver(vc, null);let /*<@3>*/wc = La.Secure.getUriFor(La.Secure.NETWORK_DATA_ENABLE_VIDEO);let /*<@22>*/xc = new NetworkEnableChangeObserver(wc, /*<@45>*/() =>{this.getVideoNetworkDataEnableValue(tc);});tc.registerObserver(xc, null);}/*<@31>*/getAllNetworkDataEnableValue(/*<@32>*/tc){La.Secure.getNumberWithDefault(tc, La.Secure.NETWORK_DATA_ENABLE_ALL, 1, /*<@46>*/(/*<@1>*/uc) =>{Qa.D(Ua, "Settings.Secure.NETWORK_DATA_ENABLE_ALL", uc);this.allNetworkDataEnableInitCompleted=true;this.allNetworkDataEnableValue=uc;this.handleNetworkDataEnableChanged();});}/*<@33>*/getVideoNetworkDataEnableValue(/*<@32>*/tc){La.Secure.getNumberWithDefault(tc, La.Secure.NETWORK_DATA_ENABLE_VIDEO, 1, /*<@47>*/(/*<@1>*/uc) =>{Qa.D(Ua, "Settings.Secure.NETWORK_DATA_ENABLE_VIDEO", uc);this.videoNetworkDataEnableInitCompleted=true;this.videoNetworkDataEnableValue=uc;this.handleNetworkDataEnableChanged();});}/*<@34>*/handleNetworkDataEnableChanged(){if(Ra.getInstance().isNetworkWiFi()) {Qa.I(Ua, "handleNetworkDataEnableChanged, wifi connected");return;}if(!this.allNetworkDataEnableInitCompleted || !this.videoNetworkDataEnableInitCompleted) {return;}Qa.I(Ua, "handleNetworkDataEnableChanged", this.allNetworkDataEnableValue, this.videoNetworkDataEnableValue);if(this.allNetworkDataEnableValue===0||this.videoNetworkDataEnableValue===0|| !Ra.getInstance().isTrafficCtrlEnabled()) {this.showDialog(Sa.TrafficPromptType.OPEN);}else {this.closeDialog(false, Sa.TrafficPromptType.OPEN);this.checkRemainingData(false);this.emit(Sa.EV_TRAFFIC_CHANGED, false);}}/*<@35>*/checkTrafficState(){Qa.I(Ua, "checkTrafficState", this.allNetworkDataEnableInitCompleted, this.allNetworkDataEnableValue, this.videoNetworkDataEnableInitCompleted, this.videoNetworkDataEnableValue, this.networkRemainingData, Ra.getInstance().isTrafficCtrlEnabled());if(Ra.getInstance().isNetworkWiFi()) {Qa.I(Ua, "checkTrafficState, wifi connected");return true;}if(this.allNetworkDataEnableValue===0||this.videoNetworkDataEnableValue===0|| !Ra.getInstance().isTrafficCtrlEnabled()) {this.showDialog(Sa.TrafficPromptType.OPEN);return false;}if(this.allNetworkDataEnableInitCompleted&&this.videoNetworkDataEnableInitCompleted) {if(this.networkRemainingData===0) {this.checkRemainingData(false);this.showDialog(Sa.TrafficPromptType.EXHAUSTION);return false;}else {this.checkRemainingData();}}return true;}/*<@36>*/checkRemainingData(/*<@2>*/tc = true){Qa.I(Ua, "checkRemainingData", this.trafficType, tc);if(!this.trafficType) {this.initTrafficType(/*<@48>*/() =>{this.getExtendedRemainingTraffic(tc);});}else {this.getExtendedRemainingTraffic(tc);}}/*<@37>*/getExtendedRemainingTraffic(/*<@2>*/tc = true){this.netQuery.getExtendedRemainingTraffic(this.trafficType, Pa.THRESHOLD_ANY, /*<@49>*/(/*<@1>*/uc, /*<@1>*/vc) =>{if(uc!==Oa.ErrorCode.SUCCESS) {Qa.I(Ua, "getExtendedRemainingTraffic failed", uc);return;}Qa.I(Ua, "getExtendedRemainingTraffic", vc);if(vc>0) {this.closeDialog(false, Sa.TrafficPromptType.EXHAUSTION);}else {if(tc) {this.showDialog(Sa.TrafficPromptType.EXHAUSTION);}this.emit(Sa.EV_TRAFFIC_CHANGED, false);}this.networkRemainingData=vc;});}/*<@38>*/showDialog(/*<@1>*/tc){if(this.dialog) {this.closeDialog(true);}Qa.I(Ua, "showDialog", tc);let /*<@21>*/uc = new Ha();uc.voiceEnabled=true;uc.once("result", /*<@50>*/(/*<@51>*/vc) =>{if(uc.type===Sa.TrafficPromptType.OPEN) {if(vc===0) {this.jumpToSetting();}else {Ta.getInstance().hidePage();}}else if(uc.type===Sa.TrafficPromptType.EXHAUSTION) {if(vc===0) {this.jumpToPersonalCenter();}else {Ta.getInstance().hidePage();}}});uc.once("close", /*<@52>*/() =>{uc.destroy(true);this.dialog=null;});this.setContentView(uc, tc);uc.show();this.dialog=uc;}/*<@39>*/setContentView(/*<@21>*/tc, /*<@1>*/uc){tc.type=uc;switch (uc) {case Sa.TrafficPromptType.OPEN:tc.title=Ka.getInstance().getString("TRAFFIC_TITLE");tc.message=Ka.getInstance().getString("TRAFFIC_OPEN");tc.buttons=/*<@55>*/[/*<@53>*/{text:Ka.getInstance().getString("TRAFFIC_BTN_OPEN"),style:Ha.ButtonStyle.Positive},/*<@54>*/{text:Ka.getInstance().getString("CANCEL")}];break;case Sa.TrafficPromptType.EXHAUSTION:tc.title=Ka.getInstance().getString("TRAFFIC_TITLE");tc.message=Ka.getInstance().getString("TRAFFIC_EXHAUSTION");tc.buttons=/*<@55>*/[/*<@53>*/{text:Ka.getInstance().getString("TRAFFIC_BTN_BUY"),style:Ha.ButtonStyle.Positive},/*<@54>*/{text:Ka.getInstance().getString("CANCEL")}];break;}let vc = tc.findViewById("alert-dialog-content");if(vc) {let wc = vc.findViewById("alert-dialog-content-message");if(wc) {wc.align=Ja.Align.Center;}}}/*<@40>*/closeDialog(/*<@2>*/tc, /*<@1>*/uc){if(this.dialog) {Qa.I(Ua, "closeDialog,", this.dialog.type, uc);if(tc||this.dialog.type===uc) {this.dialog.destroy(true);this.dialog=null;}}}/*<@41>*/jumpToSetting(){Qa.I(Ua, "jumpToSetting");let tc = new Ga("page://systemsetting.ivi.com/systemsetting");tc.eventName="network";tc.data="video.alios.cn";Ta.getInstance().sendLink(tc, /*<@58>*/(/*<@51>*/uc, /*<@51>*/vc) =>{if(uc) {Qa.I(Ua, "jumpToSetting, err ", uc);}else {Qa.I(Ua, "jumpToSetting, result ", vc);}});}/*<@42>*/jumpToPersonalCenter(){Qa.I(Ua, "jumpToPersonalCenter", this.trafficType);const tc = new Ga("page://personalcenter.ivi.com/personalcenter");tc.eventName="flowMallViewLink";let /*<@59>*/uc = /*<@59>*/{channelId:this.trafficType};tc.data=JSON.stringify(uc);Ta.getInstance().sendLink(tc, /*<@60>*/(/*<@51>*/vc, /*<@51>*/wc) =>{if(vc) {Qa.I(Ua, "jumpToPersonalCenter, err ", vc);}else {Qa.I(Ua, "jumpToPersonalCenter, result ", wc);}});}}module.exports=TrafficHelper;
