/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"utils/Utils.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/content/resource/Resource.js","name":""},{"path":"/usr/framework/yunos/device/Screen.js","name":""},{"path":"/usr/framework/yunos/ui/voice/VoiceCommand.js","name":""},{"path":"/usr/framework/yunos/ui/view/View.js","name":""},{"path":"/usr/framework/yunos/ui/gesture/TapRecognizer.js","name":""},{"path":"/usr/framework/yunos/ui/gesture/GestureRecognizer.js","name":""},{"path":"../Consts.js","name":""},{"iname":"ITapView","supers":{"k":10},"fields":{"_localProperties":15}},{"oname":"","fields":{"tapRecognizer":0,"onTapListener":0}},{"bname":"Object"},{"aname":"","element":16,"mode":"normal"},{"cname":"Utils","sfields":{"const isEmpty":20,"const getScreenWidth":21,"const getScreenHeight":22,"const registerVoiceCommand":23,"const removeVoiceCommand":26,"const setOnTapListener":27,"const md5":28,"const getDimen":29,"const getColor":30},"methods":[19]},{"fname":"constructor","ret":18},{"fname":"static isEmpty","params":{"tc":3},"ret":2},{"fname":"static getScreenWidth","ret":1},{"fname":"static getScreenHeight","ret":1},{"fname":"static registerVoiceCommand","params":{"tc":24,"uc":25,"vc":1,"wc":0,"xc":2}},{"instance_of":10},{"aname":"","element":3,"mode":"normal"},{"fname":"static removeVoiceCommand","params":{"tc":24,"uc":0}},{"fname":"static setOnTapListener","params":{"tc":24,"uc":0}},{"fname":"static md5","params":{"tc":3},"ret":3},{"fname":"static getDimen","params":{"tc":3},"ret":1},{"fname":"static getColor","params":{"tc":3},"ret":3},{"uname":"","types":[3,4]},{"aname":"","element":0,"mode":"normal"},{"fname":"","params":{"Ac":0}}],"exports":19}*/"use strict";const /*<@7>*/g = require("/usr/framework/yunos/content/resource/Resource.js");const h = require("crypto");const /*<@8>*/i = require("/usr/framework/yunos/device/Screen.js");const /*<@9>*/j = require("/usr/framework/yunos/ui/voice/VoiceCommand.js");const /*<@10>*/k = require("/usr/framework/yunos/ui/view/View.js");const /*<@11>*/l = require("/usr/framework/yunos/ui/gesture/TapRecognizer.js");const /*<@3>*/o = "Utils";class /*<@19>*/Utils{static /*<@20>*/isEmpty(/*<@31>*/tc){return !tc || !tc.length;}static /*<@21>*/getScreenWidth(){return i.getInstance().widthPixels;}static /*<@22>*/getScreenHeight(){return i.getInstance().heightPixels;}static /*<@23>*/registerVoiceCommand(/*<@24>*/tc, /*<@25>*/uc, /*<@1>*/vc, wc, /*<@2>*/xc){tc.voiceEnabled=true;tc.voiceSelectMode=k.VoiceSelectMode.Custom;let /*<@25>*/yc = /*<@25>*/[];for (let /*<@5>*/Ac = 0; Ac<uc.length; Ac++) {let /*<@3>*/Bc = g.getInstance().getString(uc[Ac]);yc.push(Bc);}let zc = new j();zc.interactorState=j.InteractorState.Idle;zc.customCommands=yc;if(vc) {zc.recognitionMode=vc;}if(xc) {zc.keepFullTimeActive=xc;}tc.addVoiceCommand(zc);tc.on("voice", /*<@33>*/(Ac) =>{let /*<@1>*/Bc = yc.indexOf(Ac._querywords);log.I(o, "VoiceCommand.onVoice, view:", tc.id, ", cmd:", Ac._querywords, ", index:", Bc);if(wc) {wc(uc[Bc], Bc, Ac);}Ac.endLocalTask();});}static /*<@26>*/removeVoiceCommand(/*<@24>*/tc, uc){tc.voiceSelectMode=undefined;if(!uc) {tc.removeAllListeners("voice");}else {tc.removeListener("voice", uc);}tc.removeAllVoiceCommands();}static /*<@27>*/setOnTapListener(/*<@24>*/tc, uc){if(!tc) {log.E(o, "setTapListener failed! view invalid.");return;}let /*<@14>*/vc = /*<@14>*/tc;if(uc) {if(vc._localProperties) {if(vc._localProperties.tapRecognizer) {vc.removeGestureRecognizer(vc._localProperties.tapRecognizer);vc._localProperties.tapRecognizer=undefined;}if(vc._localProperties.onTapListener) {vc.removeEventListener("tap", vc._localProperties.onTapListener);vc._localProperties.onTapListener=undefined;}}else {vc._localProperties={};}vc._localProperties.tapRecognizer=new l();vc.addGestureRecognizer(vc._localProperties.tapRecognizer);vc.addEventListener("tap", uc);vc._localProperties.onTapListener=uc;}else {if(vc._localProperties) {if(vc._localProperties.onTapListener) {vc.removeEventListener("tap", vc._localProperties.onTapListener);vc._localProperties.onTapListener=undefined;}if(vc._localProperties.tapRecognizer) {vc.removeGestureRecognizer(vc._localProperties.tapRecognizer);vc._localProperties.tapRecognizer=undefined;}}}}static /*<@28>*/md5(/*<@3>*/tc){if(!tc) {return " ";}let uc = h.createHash("md5");return uc.update(tc).digest("hex");}static /*<@29>*/getDimen(/*<@3>*/tc){let /*<@16>*/uc = g.getInstance().getConfig(tc);if(typeof uc==="string") {let /*<@1>*/vc = /*<@3>*/uc.indexOf("{");let /*<@1>*/wc = /*<@3>*/uc.indexOf("(", vc);let /*<@1>*/xc = /*<@3>*/uc.indexOf(")", wc);if(vc<0||wc<0||xc<0) {log.I(o, "getDimen failed, invalid value:", uc);return 0;}let /*<@3>*/yc = /*<@3>*/uc.substring(vc+1, wc);let /*<@3>*/zc = /*<@3>*/uc.substring(wc+1, xc);if(yc==="dp") {return Number.parseFloat(zc)*i.getInstance().densityFactor;}else if(yc==="px") {return Number.parseFloat(zc);}else if(yc==="sdp") {return i.getInstance().getPixelBySDp(Number.parseFloat(zc));}}else if(typeof uc==="number") {return /*<@1>*/uc;}log.I(o, "getDimen failed, unsupported type:", uc);return 0;}static /*<@30>*/getColor(/*<@3>*/tc){return g.getInstance().getValue(tc, "color.json");}}module.exports=Utils;
