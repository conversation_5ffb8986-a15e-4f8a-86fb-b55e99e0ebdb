/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"utils/log.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"cname":"Log","sfields":{"const D":9,"const I":12,"const W":13,"const E":14,"const PI":15,"const PE":16,"const traceFailure":17},"methods":[8]},{"fname":"constructor","ret":7},{"fname":"static D","params":{"tc":3,"uc":11}},{"bname":"Object"},{"aname":"","element":10,"mode":"normal"},{"fname":"static I","params":{"tc":3,"uc":11}},{"fname":"static W","params":{"tc":3,"uc":11}},{"fname":"static E","params":{"tc":3,"uc":11}},{"fname":"static PI","params":{"tc":3,"uc":11}},{"fname":"static PE","params":{"tc":3,"uc":11}},{"fname":"static traceFailure","params":{"tc":3,"uc":11}}],"exports":8}*/"use strict";const /*<@2>*/d = true;const /*<@2>*/e = true;const /*<@3>*/f = "bilibili.";class /*<@8>*/Log{static /*<@9>*/D(/*<@3>*/tc, .../*<@11>*/uc){if(d) {arguments[0]=f+tc;log.D.apply(this, arguments);}}static /*<@12>*/I(/*<@3>*/tc, .../*<@11>*/uc){if(d) {arguments[0]=f+tc;log.I.apply(this, arguments);}}static /*<@13>*/W(/*<@3>*/tc, .../*<@11>*/uc){if(d) {arguments[0]=f+tc;log.W.apply(this, arguments);}}static /*<@14>*/E(/*<@3>*/tc, .../*<@11>*/uc){if(d) {arguments[0]=f+tc;log.E.apply(this, arguments);}}static /*<@15>*/PI(/*<@3>*/tc, .../*<@11>*/uc){if(e) {arguments[0]=f+tc;log.I.apply(this, arguments);}}static /*<@16>*/PE(/*<@3>*/tc, .../*<@11>*/uc){if(e) {arguments[0]=f+tc;log.E.apply(this, arguments);}}static /*<@17>*/traceFailure(/*<@3>*/tc, .../*<@11>*/uc){log.E(f+tc, ">>>>>>CardShell Failure");log.E(f+tc, uc);console.trace(uc);log.E(f+tc, "<<<<<<CardShell Failure");}}module.exports=Log;
