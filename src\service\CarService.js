/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/CarService.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/core/EventEmitter.js","name":""},{"path":"/usr/framework/yunos/platform/auto/carservice/CarPropertyManager.js","name":""},{"path":"/usr/framework/yunos/platform/auto/carservice/CarPropertyError.js","name":""},{"path":"/usr/framework/yunos/platform/auto/carservice/CarControlManager.js","name":""},{"path":"/usr/framework/sysprop/sysprop.js","name":""},{"path":"../Consts.js","name":""},{"path":"../utils/log.js","name":""},{"cname":"CarService","supers":{"sa":7},"fields":{"carPropertyManager":16,"speed":1,"gearStatus":1,"carControlManager":17,"isAmbientLightEnable":2,"settingVideoStatus":3,"hasVideoLimitSwitch":-2},"sfields":{"static instance":14,"const getInstance":18,"const releaseInstance":19},"methods":[15,20,21,22,23,24,25,26]},{"fname":"constructor","ret":14,"hobj":1},{"instance_of":8},{"instance_of":10},{"fname":"static getInstance","ret":14},{"fname":"static releaseInstance"},{"fname":"init"},{"fname":"isDrivingWarning","ret":2},{"fname":"getVehicleSpeed","ret":1},{"fname":"getVehicleGearStatus","ret":1},{"fname":"isParkingStatus","ret":2},{"fname":"getAmbientLightStatus","ret":2},{"fname":"destroy","hobj":1},{"fname":"","params":{"tc":28,"uc":0}},{"instance_of":9},{"fname":"","params":{"tc":28,"uc":0}},{"fname":"","params":{"tc":28,"uc":0}},{"fname":"","params":{"tc":28,"uc":0}},{"fname":""},{"fname":"","params":{"tc":28,"uc":2}}],"exports":15}*/"use strict";const /*<@7>*/sa = require("/usr/framework/yunos/core/EventEmitter.js");const /*<@8>*/ta = require("/usr/framework/yunos/platform/auto/carservice/CarPropertyManager.js");const ua = ta.PropertyId;const va = ta.VehicleArea;const /*<@10>*/xa = require("/usr/framework/yunos/platform/auto/carservice/CarControlManager.js");const /*<@11>*/ya = require("/usr/framework/sysprop/sysprop.js");const /*<@12>*/za = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@13>*/Aa = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@3>*/Ba = "CarService";const /*<@3>*/Ca = "persist.setting.VideoStatus";var SettingVideoStatus;(function (SettingVideoStatus){SettingVideoStatus["DEFAULT"]="-1";SettingVideoStatus["OFF"]="0";SettingVideoStatus["ON"]="1";})(SettingVideoStatus||(SettingVideoStatus={}));const /*<@5>*/Da = 1;const /*<@5>*/Ea = 3;class /*<@15>*/CarService extends sa{constructor(){super();this.hasVideoLimitSwitch=false;this.hasVideoLimitSwitch=ya.get("persist.sys.VideoStatus")==="1";}static /*<@18>*/getInstance(){if(!this.instance) {this.instance=new CarService();}return this.instance;}static /*<@19>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@20>*/init(){Aa.I(Ba, "init");this.speed=0;this.gearStatus=Da;this.isAmbientLightEnable=true;this.carPropertyManager=new ta();if(!this.hasVideoLimitSwitch) {this.carPropertyManager.getProperty(ua.SYS_GEAR_STATUS, va.GLOBAL, 1000, /*<@27>*/(/*<@28>*/tc, uc) =>{if(tc) {Aa.D(Ba, "get SYS_GEAR_STATUS", tc);return;}Aa.D(Ba, "get SYS_GEAR_STATUS", uc);if(uc&&uc.value>0) {if(uc.value===Ea) {this.gearStatus=Da;}else {this.gearStatus=uc./*<@1>*/value;}}});this.carPropertyManager.subscribe(ua.SYS_GEAR_STATUS, /*<@29>*/(/*<@28>*/tc, uc) =>{if(tc) {Aa.D(Ba, "subscribe SYS_GEAR_STATUS", tc);return;}Aa.I(Ba, "subscribe SYS_GEAR_STATUS", uc);if(uc&&uc.value>0) {if(uc.value===Ea) {this.gearStatus=Da;this.emit(za.EV_GEAR_STATUS_CHANGED, this.gearStatus);}else {this.gearStatus=uc./*<@1>*/value;this.emit(za.EV_GEAR_STATUS_CHANGED, this.gearStatus);}}});}else {this.carPropertyManager.getProperty(ua.SYS_VEHICLE_SPEED, va.GLOBAL, 1000, /*<@30>*/(/*<@28>*/tc, uc) =>{if(tc) {Aa.D(Ba, "get SYS_VEHICLE_SPEED", tc);return;}Aa.D(Ba, "get SYS_VEHICLE_SPEED", uc);if(uc&&uc./*<@1>*/value>=0) {let /*<@1>*/vc = Math.floor(uc./*<@1>*/value);this.speed=vc;this.emit(za.EV_DRIVING_WARNING, this.isDrivingWarning());}});this.carPropertyManager.subscribe(ua.SYS_VEHICLE_SPEED, /*<@31>*/(/*<@28>*/tc, uc) =>{if(tc) {Aa.D(Ba, "subscribe SYS_VEHICLE_SPEED", tc);return;}Aa.D(Ba, "subscribe SYS_VEHICLE_SPEED", uc);if(uc&&uc./*<@1>*/value>=0) {let /*<@1>*/vc = Math.floor(uc./*<@1>*/value);if(vc!==this.speed) {this.speed=vc;this.emit(za.EV_DRIVING_WARNING, this.isDrivingWarning());}}});this.settingVideoStatus=ya.get(Ca, SettingVideoStatus.OFF);this.emit(za.EV_DRIVING_WARNING, this.isDrivingWarning());ya.watch(Ca, /*<@32>*/() =>{let /*<@3>*/tc = ya.get(Ca, SettingVideoStatus.OFF);Aa.D(Ba, "driving video status changed,", tc);this.settingVideoStatus=tc;this.emit(za.EV_DRIVING_WARNING, this.isDrivingWarning(), tc===SettingVideoStatus.ON);});}this.carControlManager=new xa();this.carControlManager.getAmbientLightStatus(/*<@33>*/(/*<@28>*/tc, /*<@2>*/uc) =>{if(tc) {Aa.D(Ba, "getAmbientLightStatus", tc);return;}Aa.D(Ba, "getAmbientLightStatus", uc);this.isAmbientLightEnable=uc;});}/*<@21>*/isDrivingWarning(){Aa.D(Ba, "isDrivingWarning,", this.speed, this.settingVideoStatus);return this.speed>0&&this.settingVideoStatus===SettingVideoStatus.OFF;}/*<@22>*/getVehicleSpeed(){Aa.D(Ba, "getVehicleSpeed, speed:", this.speed);return this.speed;}/*<@23>*/getVehicleGearStatus(){Aa.D(Ba, "getVehicleGearStatus, gearStatus:", this.gearStatus);if(this.hasVideoLimitSwitch) {return Da;}return this.gearStatus;}/*<@24>*/isParkingStatus(){Aa.D(Ba, "isParkingStatus, gearStatus:", this.gearStatus);return this.gearStatus===Da;}/*<@25>*/getAmbientLightStatus(){Aa.D(Ba, "getAmbientLightStatus, isEnable:", this.isAmbientLightEnable);return this.isAmbientLightEnable;}/*<@26>*/destroy(){super.destroy();Aa.I(Ba, "destroy");if(this.carPropertyManager) {this.carPropertyManager.unsubscribe(ua.SYS_GEAR_STATUS);this.carPropertyManager.destroy();}if(this.carControlManager) {this.carControlManager.destroy();}}}module.exports=CarService;
