/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"utils/UserTrackHelper.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/datadriver/DataAgent.js","name":""},{"path":"log.js","name":""},{"path":"../Consts.js","name":""},{"cname":"UserTrackHelper","fields":{"TriggerType":12},"sfields":{"const getInstance":13},"methods":[11,14,16,17,18,19]},{"fname":"constructor","params":{"pageName":3},"ret":10},{"oname":"","fields":{"CLICK":-3,"SLIDE":-3,"HARDKEY":-3,"VOICE":-3}},{"fname":"static getInstance","params":{"tc":3},"ret":10},{"fname":"clickButton","params":{"tc":3,"uc":3,"vc":15}},{"bname":"Object"},{"fname":"sendEvent","params":{"tc":3,"uc":3,"vc":15}},{"fname":"enterPage","params":{"tc":3,"uc":15}},{"fname":"leavePage","params":{"tc":3,"uc":15}},{"fname":"getDataAgent","private":1,"params":{"tc":3},"ret":0},{"oname":"","fields":{"TrackVersion":-3}}],"exports":11}*/"use strict";const /*<@7>*/Wa = require("/usr/framework/yunos/datadriver/DataAgent.js");const /*<@8>*/Xa = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@9>*/Ya = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@3>*/Za = "UserTrackHelper";const /*<@2>*/ab = true;const /*<@3>*/bb = "c4b7872b3960795d";const /*<@3>*/cb = "1.0.0";let /*<@10>*/db = null;class /*<@11>*/UserTrackHelper{static /*<@13>*/getInstance(/*<@3>*/tc){if(!db) {db=new UserTrackHelper(tc);}return db;}constructor(/*<@3>*/pageName){this.getDataAgent(pageName).configure(bb, /*<@20>*/{"TrackVersion":cb});}/*<@14>*/clickButton(/*<@3>*/tc, /*<@3>*/uc, /*<@15>*/vc){vc=vc||{};this.getDataAgent(tc).clickButton(uc, vc);if(ab) {Xa.I(Za, "clickButton", tc, uc, vc);}}/*<@16>*/sendEvent(/*<@3>*/tc, /*<@3>*/uc, /*<@15>*/vc){vc=vc||{};this.getDataAgent(tc).sendEvent(uc, vc);if(ab) {Xa.I(Za, "sendEvent", uc, tc, vc);}}/*<@17>*/enterPage(/*<@3>*/tc, /*<@15>*/uc){uc=uc||{};this.getDataAgent(tc).enterPage(uc);if(ab) {Xa.I(Za, "enterPage", tc, uc);}}/*<@18>*/leavePage(/*<@3>*/tc, /*<@15>*/uc){uc=uc||{};this.getDataAgent(tc).leavePage(uc);if(ab) {Xa.I(Za, "leavePage", tc, uc);}}/*<@19>*/getDataAgent(/*<@3>*/tc){if(typeof tc!=="string"||tc.length===0) {tc=Ya.PAGE_URL;}return Wa.getInstance(tc);}}module.exports=UserTrackHelper;
