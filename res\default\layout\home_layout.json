{"container": {"type": "RelativeLayout", "params": {"id_webview": {"align": {"left": "parent", "top": "parent", "right": "parent", "bottom": "parent"}}, "id_network_container": {"align": {"left": "parent", "top": "parent", "right": "parent", "bottom": "parent"}}, "id_warning_page": {"align": {"left": "parent", "top": "parent", "right": "parent", "bottom": "parent"}}}}, "network_container": {"type": "RelativeLayout", "params": {"0": {"align": {"center": "parent", "middle": "parent"}}}}, "network_error": {"type": "RelativeLayout", "params": {"id_error_icon": {"align": {"center": "parent", "top": "parent"}}, "id_error_text": {"align": {"center": "parent", "top": {"target": "id_error_icon", "side": "bottom"}}, "margin": {"top": "{sdp(15)}"}}, "id_error_btn": {"align": {"left": "parent", "right": "parent", "bottom": "parent"}}}}}