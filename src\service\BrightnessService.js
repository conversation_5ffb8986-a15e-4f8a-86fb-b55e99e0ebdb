/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/BrightnessService.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/platform/auto/carservice/CarBrightnessManager.js","name":""},{"path":"../utils/log.js","name":""},{"cname":"BrightnessService","fields":{"manager":11,"brightness":1},"sfields":{"static instance":9,"const getInstance":12,"const releaseInstance":13},"methods":[10,14,15,16,17]},{"fname":"constructor","ret":9},{"instance_of":7},{"fname":"static getInstance","ret":9},{"fname":"static releaseInstance"},{"fname":"init"},{"fname":"getScreenBrightness","ret":1},{"fname":"setScreenBrightness","params":{"tc":1}},{"fname":"destroy"},{"fname":"","params":{"tc":1,"uc":1}},{"fname":"","params":{"tc":1,"uc":1}},{"fname":"","params":{"uc":1,"vc":1}},{"fname":"","params":{"uc":1,"vc":1}}],"exports":10}*/"use strict";const /*<@7>*/S = require("/usr/framework/yunos/platform/auto/carservice/CarBrightnessManager.js");const /*<@8>*/T = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@3>*/U = "BrightnessService";class /*<@10>*/BrightnessService{static /*<@12>*/getInstance(){if(!this.instance) {this.instance=new BrightnessService();}return this.instance;}static /*<@13>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@14>*/init(){T.I(U, "init");this.brightness=0;this.manager=new S();this.manager.init();this.manager.getBrightnessValue(/*<@18>*/(/*<@1>*/tc, /*<@1>*/uc) =>{T.I(U, "getBrightnessValue, errorCode:", tc, ", brightness:", uc);if(tc===S.ErrorCode.SUCCESS) {this.brightness=uc;}});this.manager.onBrightnessValue(/*<@19>*/(/*<@1>*/tc, /*<@1>*/uc) =>{T.I(U, "onBrightnessValue, errorCode:", tc, ", brightness:", uc);if(tc===S.ErrorCode.SUCCESS) {this.brightness=uc;}});}/*<@15>*/getScreenBrightness(){T.D(U, "getScreenBrightness, brightness:", this.brightness);return this.brightness;}/*<@16>*/setScreenBrightness(/*<@1>*/tc){T.D(U, "setScreenBrightness,", tc);this.manager.setScreenBrightnessValue(tc, /*<@20>*/(/*<@1>*/uc, /*<@1>*/vc) =>{T.I(U, "setScreenBrightnessValue, errorCode:", uc, ", status:", vc);if(uc===S.ErrorCode.SUCCESS) {this.brightness=tc;}});this.manager.setIpkBrightnessValue(tc, /*<@21>*/(/*<@1>*/uc, /*<@1>*/vc) =>{T.I(U, "setIpkBrightnessValue, errorCode:", uc, ", status:", vc);if(uc===S.ErrorCode.SUCCESS) {this.brightness=tc;}});}/*<@17>*/destroy(){T.I(U, "destroy");if(this.manager) {this.manager.destroy();this.manager=null;}}}module.exports=BrightnessService;
