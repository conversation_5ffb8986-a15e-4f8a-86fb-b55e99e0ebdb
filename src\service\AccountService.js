/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/AccountService.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/core/account/AccountManager.js","name":""},{"path":"../utils/log.js","name":""},{"cname":"AccountService","fields":{"_userName":3},"sfields":{"static instance":9,"const getInstance":11,"const releaseInstance":12},"methods":[10,13,14,15,16]},{"fname":"constructor","ret":9},{"fname":"static getInstance","ret":9},{"fname":"static releaseInstance"},{"fname":"init"},{"fname":"getAccountName"},{"fname":"get userName","ret":3},{"fname":"destroy"},{"fname":"","params":{"uc":0}}],"exports":10}*/"use strict";const /*<@7>*/J = require("/usr/framework/core/account/AccountManager.js");const /*<@8>*/K = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@3>*/L = "account.zxq.com";const /*<@3>*/M = "AccountService";class /*<@10>*/AccountService{static /*<@11>*/getInstance(){if(!this.instance) {this.instance=new AccountService();}return this.instance;}static /*<@12>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@13>*/init(){this.getAccountName();}/*<@14>*/getAccountName(){let tc = J.getInstance();tc.getCurrentAccount(L, /*<@17>*/(uc) =>{if(uc) {this._userName=uc.userName;K.I(M, "getCurrentAccount, id:", this._userName);}});}get /*<@15>*/userName(){if(this._userName) {return this._userName;}else {this.getAccountName();}}/*<@16>*/destroy(){K.I(M, "destroy");}}module.exports=AccountService;
