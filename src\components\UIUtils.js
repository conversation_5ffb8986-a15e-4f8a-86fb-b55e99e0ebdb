/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"components/UIUtils.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/graphics/Color.js","name":""},{"path":"/usr/framework/yunos/device/Screen.js","name":""},{"path":"/usr/framework/yunos/content/resource/Resource.js","name":""},{"path":"/usr/framework/yunos/ui/theme/Theme.js","name":""},{"cname":"UIUtils","sfields":{"const dpToPixel":13,"const sdpToPixel":14,"const isPortraitScreen":15,"const getConfig":16,"const getPixel":18},"methods":[12]},{"fname":"constructor","ret":11},{"fname":"static dpToPixel","params":{"tc":1},"ret":1},{"fname":"static sdpToPixel","params":{"tc":1},"ret":1},{"fname":"static isPortraitScreen","ret":2},{"fname":"static getConfig","params":{"tc":3,"uc":0},"ret":0},{"aname":"","element":0,"mode":"normal"},{"fname":"static getPixel","params":{"tc":0},"ret":1},{"oname":"","fields":{"UIUtils":12}}],"exports":19}*/"use strict";Object.defineProperty(exports, "__esModule", {value:true});const /*<@8>*/fb = require("/usr/framework/yunos/device/Screen.js");const /*<@9>*/gb = require("/usr/framework/yunos/content/resource/Resource.js");const ib = fb.getInstance();const jb = log("bilibili.UIUtils");class /*<@12>*/UIUtils{static /*<@13>*/dpToPixel(/*<@1>*/tc){return ib.getPixelByDp(tc);}static /*<@14>*/sdpToPixel(/*<@1>*/tc){return ib.getPixelBySDp(tc);}static /*<@15>*/isPortraitScreen(){return ib.heightPixels>ib.widthPixels;}static /*<@16>*/getConfig(/*<@3>*/tc, uc){let vc;try {vc=gb.getInstance().getConfig(tc);}catch (wc) {jb.W(`[getConfig] failed to get config (${tc}) from resources`);vc=uc;}return vc;}static /*<@18>*/getPixel(tc){if(typeof tc==="number") {return tc;}if(typeof tc==="string") {if(tc.endsWith("px")) {return parseInt(tc.substr(tc.length-2));}if(tc.startsWith("{dp(")) {let /*<@1>*/uc = parseInt(tc.substr(4, tc.length-6));return UIUtils.dpToPixel(uc);}if(tc.startsWith("{sdp(")) {let /*<@1>*/uc = parseInt(tc.substr(5, tc.length-7));return UIUtils.sdpToPixel(uc);}}return 0;}}exports.UIUtils=UIUtils;
