/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/AudioService.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/core/EventEmitter.js","name":""},{"path":"/usr/framework/yunos/device/AudioManager.js","name":""},{"path":"../utils/log.js","name":""},{"path":"../Consts.js","name":""},{"cname":"AudioService","supers":{"N":7},"fields":{"isMute":-2,"onAudioSessionChange":0},"sfields":{"static instance":11,"const getInstance":13,"const releaseInstance":14},"methods":[12,15,17,18,19,20,21,22,23,24,25]},{"fname":"constructor","ret":11,"hobj":1},{"fname":"static getInstance","ret":11},{"fname":"static releaseInstance"},{"fname":"onRingerModeChange","private":1,"params":{"tc":16,"uc":3}},{"uname":"","types":[1,3]},{"fname":"onAudioMuteUpdate","private":1,"params":{"tc":3,"uc":2,"vc":3}},{"fname":"setRingerModeNormal"},{"fname":"isRingerModeNormal","ret":2},{"fname":"requestAudioSession","ret":1},{"fname":"getAudioVolumeLevel","ret":1},{"fname":"setAudioVolumeLevel","params":{"tc":1}},{"fname":"isAppGainAudioSession","ret":2},{"fname":"isMediaGainAudioSession","ret":2},{"fname":"destroy","hobj":1},{"fname":"","params":{"uc":1,"vc":3}},{"aname":"","element":3,"mode":"normal"}],"exports":12}*/"use strict";const /*<@7>*/N = require("/usr/framework/yunos/core/EventEmitter.js");const /*<@8>*/O = require("/usr/framework/yunos/device/AudioManager.js");const /*<@9>*/P = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@10>*/Q = require("/opt/app/bilibili.alios.cn/src/Consts.js");const /*<@3>*/R = "AudioService";class /*<@12>*/AudioService extends N{constructor(){super();this.isMute=false;const tc = O.getInstance();if(tc) {this.isMute=tc.getAudioRingerMode()!==O.RingerMode.RINGER_MODE_NORMAL;tc.registerAudioVolumeListener();tc.registerAudioRingerModeListener();tc.on(O.RingerModeChangedEvent.RINGER_MODE_CHANGED, this.onRingerModeChange.bind(this));}}static /*<@13>*/getInstance(){if(!this.instance) {this.instance=new AudioService();}return this.instance;}static /*<@14>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@15>*/onRingerModeChange(/*<@16>*/tc, /*<@3>*/uc){P.I(R, "onRingerModeChange", tc, uc);let /*<@2>*/vc = tc!==O.RingerMode.RINGER_MODE_NORMAL;if(this.isMute===vc) {P.I(R, "onRingerModeChange, ignore");return;}this.isMute=vc;this.emit(Q.EV_AUDIO_RINGER_MODE_CHANGED, vc);}/*<@17>*/onAudioMuteUpdate(/*<@3>*/tc, /*<@2>*/uc, /*<@3>*/vc){P.I(R, "onAudioMuteUpdate", tc, uc, vc);if(this.isMute===uc) {P.I(R, "onAudioMuteUpdate, ignore");return;}this.isMute=uc;this.emit(Q.EV_AUDIO_RINGER_MODE_CHANGED, uc);}/*<@18>*/setRingerModeNormal(){const tc = O.getInstance();if(tc) {if(tc.getAudioRingerMode()!==O.RingerMode.RINGER_MODE_NORMAL) {tc.setAudioRingerMode(O.RingerMode.RINGER_MODE_NORMAL);}}}/*<@19>*/isRingerModeNormal(){const tc = O.getInstance();if(tc) {return tc.getAudioRingerMode()===O.RingerMode.RINGER_MODE_NORMAL;}return false;}/*<@20>*/requestAudioSession(){if(!this.onAudioSessionChange) {this.onAudioSessionChange=/*<@26>*/(/*<@1>*/uc, /*<@3>*/vc) =>{P.I(R, "onAudioSessionChange,", uc, vc);let /*<@2>*/wc = vc==="browser_media_player";let /*<@2>*/xc = false;if(uc===O.AudioSessionType.AUDIOSESSION_CHANGE_RESUMED_BY_SYSTEM) {xc=true;}else if(uc===O.AudioSessionType.AUDIOSESSION_CHANGE_STOPPED_BY_OTHER) {xc=false;if(wc) {return;}}else if(uc===O.AudioSessionType.AUDIOSESSION_CHANGE_PAUSED_BY_OTHER) {xc=false;if(wc) {return;}}else if(uc===O.AudioSessionType.AUDIOSESSION_CHANGE_LOWERED_BY_OTHER) {return;}this.emit(Q.EV_AUDIO_SESSION_CHANGED, xc, vc);};}const tc = O.getInstance();if(tc) {return tc.requestAudioSession(O.StreamType.AUDIO_STREAM_MUSIC, O.AudioSessionType.AUDIOSESSION_REQUEST_STOP_OTHERS, Q.PACKAGE_NAME, this.onAudioSessionChange);}return O.AudioSessionResult.AUDIOSESSION_RESULT_FAILED;}/*<@21>*/getAudioVolumeLevel(){const tc = O.getInstance();if(tc) {return tc.getAudioVolumeLevel(O.StreamType.AUDIO_STREAM_MUSIC);}return 0;}/*<@22>*/setAudioVolumeLevel(/*<@1>*/tc){const uc = O.getInstance();if(uc) {let /*<@1>*/vc = uc.getMaxAudioVolumeLevel(O.StreamType.AUDIO_STREAM_MUSIC);let /*<@1>*/wc = uc.getAudioVolumeLevel(O.StreamType.AUDIO_STREAM_MUSIC);let /*<@1>*/xc = tc;P.I(R, "setAudioVolumeLevel", vc, wc, xc);if(xc>vc) {xc=vc;}uc.setAudioVolumeLevel(O.StreamType.AUDIO_STREAM_MUSIC, xc, O.AdjustFlag.FLAG_NONE);}}/*<@23>*/isAppGainAudioSession(){let /*<@3>*/tc = "";const uc = O.getInstance();if(uc) {tc=uc.getTopSessionClientName();}P.D(R, "isAppGainAudioSession, clientName", tc);return tc==Q.PACKAGE_NAME;}/*<@24>*/isMediaGainAudioSession(){let /*<@27>*/tc = Q.AppType.MEDIA.value;let /*<@3>*/uc = "";const vc = O.getInstance();if(vc) {uc=vc.getTopSessionClientName();}P.D(R, "isMediaGainAudioSession, clientName:", uc);for (let /*<@5>*/wc = 0; wc<tc.length; wc++) {if(uc===tc[wc]) {return true;}}return false;}/*<@25>*/destroy(){super.destroy();P.I(R, "destroy");const tc = O.getInstance();if(tc) {tc.unregisterAudioVolumeListener();tc.unregisterAudioRingerModeListener();tc.removeAllListeners(O.VolumeListenerEvent.STREAM_MUTE_UPDATE);tc.removeAllListeners(O.RingerModeChangedEvent.RINGER_MODE_CHANGED);}}}module.exports=AudioService;
