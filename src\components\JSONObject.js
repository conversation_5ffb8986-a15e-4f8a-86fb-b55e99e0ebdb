/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"components/JSONObject.js"},"types":["any","number","boolean","string","void","int","object",{"iname":"JSONArray","supers":{"Array":9}},{"bname":"T"},{"aname":"","element":8,"mode":"normal"},{"uname":"","types":[1,2,3,6,7]},{"uname":"","types":[1,2,3,4]},{"uname":"","types":[1,2,3]},{"uname":"","types":[6,7,12]},{"uname":"","types":[1,2,3,6,7]},{"cname":"JSO<PERSON>Hel<PERSON>","sfields":{"const clone":17,"const toJSONObject":18,"const to":20,"const isEmptyObject":21,"const forEach":22},"methods":[16]},{"fname":"constructor","ret":15},{"fname":"static clone","params":{"tc":6,"uc":6},"ret":6},{"fname":"static toJSONObject","params":{"tc":19},"ret":6},{"bname":"Object"},{"fname":"static to","params":{"tc":6},"ret":0},{"fname":"static isEmptyObject","params":{"tc":6},"ret":2},{"fname":"static forEach","params":{"tc":6,"uc":0}},{"uname":"","types":[4,19]},{"oname":"","fields":{"JSONObject":6,"JSONPrimitive":11,"JSONValue":13,"JSONArray":7,"JSONHelper":16}}],"exports":24}*/"use strict";Object.defineProperty(exports, "__esModule", {value:true});class /*<@16>*/JSONHelper{static /*<@17>*/clone(/*<@6>*/tc, /*<@6>*/uc = /*<@6>*/{}){return Object.assign(uc, tc);}static /*<@18>*/toJSONObject(/*<@23>*/tc){if(typeof tc==="undefined"||tc===null) {return {};}return tc;}static /*<@20>*/to(/*<@6>*/tc){return /*<@19>*/tc;}static /*<@21>*/isEmptyObject(/*<@6>*/tc){if(tc===null||tc===undefined) {return true;}if(typeof tc!=="object") {return true;}return Object.keys(tc).length===0&&tc.constructor===Object;}static /*<@22>*/forEach(/*<@6>*/tc, uc){for(const /*<@3>*/vc in tc) {if(Object.prototype.hasOwnProperty.call(tc, vc)) {uc(vc, tc[vc]);}}}}exports.JSONHelper=JSONHelper;
