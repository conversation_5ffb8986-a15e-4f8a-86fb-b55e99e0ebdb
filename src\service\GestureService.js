/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"service/GestureService.js","sound":1},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/core/EventEmitter.js","name":""},{"path":"/usr/framework/yunos/ui/view/WebView.js","name":""},{"path":"../utils/log.js","name":""},{"cname":"GestureService","supers":{"pc":7},"fields":{"webview":12},"sfields":{"static instance":10,"const getInstance":13,"const releaseInstance":14},"methods":[11,15,16]},{"fname":"constructor","ret":10,"hobj":1},{"instance_of":8},{"fname":"static getInstance","ret":10},{"fname":"static releaseInstance"},{"fname":"init","params":{"tc":12}},{"fname":"destroy","hobj":1},{"fname":""}],"exports":11}*/"use strict";const /*<@7>*/pc = require("/usr/framework/yunos/core/EventEmitter.js");const /*<@9>*/rc = require("/opt/app/bilibili.alios.cn/src/utils/log.js");const /*<@3>*/sc = "GestureService";class /*<@11>*/GestureService extends pc{constructor(){super();}static /*<@13>*/getInstance(){if(!this.instance) {this.instance=new GestureService();}return this.instance;}static /*<@14>*/releaseInstance(){if(this.instance) {this.instance.destroy();this.instance=null;}}/*<@15>*/init(/*<@12>*/tc){rc.I(sc, "init");tc.on("touchstart", /*<@17>*/() =>{rc.I(sc, "touch");});this.webview=tc;}/*<@16>*/destroy(){super.destroy();rc.I(sc, "destroy");if(this.webview) {this.webview.removeAllListeners("touchstart");}}}module.exports=GestureService;
