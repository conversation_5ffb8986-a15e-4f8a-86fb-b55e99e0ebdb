/* Copyright (C) 2016-2022 Banma Technologies Co.,Ltd. All Rights Reserved. */
/*<@leixing>{"header":{"version":"3.0.3","filename":"components/VideoLimitView.js"},"types":["any","number","boolean","string","void","int","object",{"path":"/usr/framework/yunos/ui/view/CompositeView.js","name":""},{"path":"/usr/framework/yunos/ui/view/TextView.js","name":""},{"path":"/usr/framework/yunos/ui/view/View.js","name":""},{"path":"/usr/framework/extend/hdt/control/ButtonBM.js","name":""},{"path":"/usr/framework/yunos/ui/layout/RelativeLayout.js","name":""},{"path":"/usr/framework/yunos/ui/view/SVGView.js","name":""},{"path":"/usr/framework/yunos/content/resource/Resource.js","name":""},{"path":"/usr/framework/yunos/ui/gesture/TapRecognizer.js","name":""},{"path":"/usr/framework/yunos/ui/event/GestureEvent.js","name":""},{"path":"UIUtils.js","name":"UIUtils"},{"path":"UIUtils.js","name":""},{"cname":"VideoLimitView","supers":{"kb":7},"fields":{"infoBox":-20,"imageView":-21,"errorText":-22,"errorDetailText":-22,"errorButton":-23,"tipsView":22,"prevTapTime":-1,"tipsChangeTimer":0,"timeIndex":-5},"methods":[19,24,25,26,28,29,30,31]},{"fname":"constructor","ret":18,"hobj":1},{"instance_of":7},{"instance_of":12},{"instance_of":8},{"instance_of":10},{"fname":"setErrorButtonStyle","private":1},{"fname":"changeStyleByMode","private":1,"params":{"tc":2}},{"fname":"show","params":{"tc":2,"uc":27,"vc":27}},{"bname":"Function"},{"fname":"hide"},{"fname":"getTipsText","private":1,"params":{"tc":1},"ret":3},{"fname":"clearTipsChangeTimer","private":1},{"fname":"destroy","hobj":1},{"oname":"","fields":{"top":-3,"center":-3}},{"oname":"","fields":{"target":-5,"side":-3}},{"oname":"","fields":{"top":-33,"center":-3}},{"oname":"","fields":{"top":-1}},{"oname":"","fields":{"left":-3,"right":-3,"middle":-3}},{"oname":"","fields":{"side":-3,"target":-5}},{"oname":"","fields":{"top":-37,"left":-3,"right":-3}},{"fname":"","params":{"tc":40,"uc":40,"vc":1}},{"bname":"Object"},{"oname":"","fields":{"color":-3,"background":-3}},{"oname":"","fields":{"normal":-41,"pressed":-41}},{"oname":"","fields":{"top":-5}},{"fname":"","params":{"wc":45}},{"instance_of":15},{"fname":""},{"fname":""},{"oname":"","fields":{"time":-3}}],"exports":18}*/"use strict";const /*<@7>*/kb = require("/usr/framework/yunos/ui/view/CompositeView.js");const /*<@8>*/lb = require("/usr/framework/yunos/ui/view/TextView.js");const /*<@9>*/mb = require("/usr/framework/yunos/ui/view/View.js");const /*<@10>*/nb = require("/usr/framework/extend/hdt/control/ButtonBM.js");const /*<@11>*/ob = require("/usr/framework/yunos/ui/layout/RelativeLayout.js");const /*<@12>*/pb = require("/usr/framework/yunos/ui/view/SVGView.js");const /*<@13>*/qb = require("/usr/framework/yunos/content/resource/Resource.js");const /*<@14>*/rb = require("/usr/framework/yunos/ui/gesture/TapRecognizer.js");const /*<@17>*/UIUtils_1 = require("/opt/app/bilibili.alios.cn/src/components/UIUtils.js");const tb = UIUtils_1.UIUtils.getConfig("videoLimitView");const ub = log("bilibili.VideoLimitView");module.exports=class /*<@19>*/VideoLimitView extends kb{constructor(){super();this.infoBox=new kb();this.prevTapTime=0;this.tipsChangeTimer=null;this.timeIndex=0;this.id="videoLimitView";this.infoBox.layout=new ob();this.imageView=new pb();this.imageView.width=UIUtils_1.UIUtils.getPixel(tb.imageWidth);this.imageView.height=UIUtils_1.UIUtils.getPixel(tb.imageWidth);this.imageView.src=qb.getInstance().getImageSrc("images/ic_speed_over_warning.svg");this.infoBox.addChild(this.imageView);this.errorText=new lb();this.errorText.fontFamily="MYingHei_18030-Heavy";this.errorText.fontSize=UIUtils_1.UIUtils.getPixel(tb.errorTextFontSize);this.errorText.height=UIUtils_1.UIUtils.getPixel(tb.errorTextHeight);this.errorText.color=tb./*<@3>*/errorTextColor;this.errorText.align=lb.Align.Center;this.errorText.text=qb.getInstance().getString("VIDEO_LIMIT_TEXT");this.infoBox.addChild(this.errorText);this.errorDetailText=new lb();this.errorDetailText.fontFamily="MYingHei_18030-Medium";this.errorDetailText.fontSize=UIUtils_1.UIUtils.getPixel(tb.errorDetailTextFontSize);this.errorDetailText.height=UIUtils_1.UIUtils.getPixel(tb.errorTextHeight);this.errorDetailText.color=tb./*<@3>*/errorDetailTextColor;this.errorDetailText.align=lb.Align.Center;this.errorDetailText.text=qb.getInstance().getString("VIDEO_LIMIT_DETAIL_TEXT");this.infoBox.addChild(this.errorDetailText);this.errorButton=new nb();this.errorButton.contentType=nb.ContentType.Text;this.errorButton.colorType=nb.ColorType.Secondary;this.errorButton.buttonType=nb.ButtonType.Ghost;this.errorButton.width=UIUtils_1.UIUtils.getPixel(tb.errorButtonWidth);this.errorButton.height=UIUtils_1.UIUtils.getPixel(tb.errorButtonHeight);this.errorButton.fontSize=UIUtils_1.UIUtils.getPixel(tb.errorButtonFontSize);this.errorButton.text=qb.getInstance().getString("GENERAL_SETTINGS");this.setErrorButtonStyle();this.infoBox.addChild(this.errorButton);this.infoBox.layout.setLayoutParam(0, "align", /*<@32>*/{top:"parent",center:"parent",});this.infoBox.layout.setLayoutParam(1, "align", /*<@34>*/{top:/*<@33>*/{target:0,side:"bottom"},center:"parent"});this.infoBox.layout.setLayoutParam(2, "align", /*<@34>*/{top:/*<@33>*/{target:1,side:"bottom"},center:"parent"});this.infoBox.layout.setLayoutParam(2, "margin", /*<@35>*/{top:UIUtils_1.UIUtils.getPixel(tb.errorDetailTextTopMargin)});this.infoBox.layout.setLayoutParam(3, "align", /*<@34>*/{top:/*<@33>*/{target:2,side:"bottom"},center:"parent"});this.infoBox.layout.setLayoutParam(3, "margin", /*<@35>*/{top:UIUtils_1.UIUtils.getPixel(tb.errorButtonTopMargin)});this.addChild(this.infoBox);this.background=tb./*<@3>*/background;this.opacity=0.9;this.borderRadius=UIUtils_1.UIUtils.getPixel(tb.borderRadius);this.tipsView=new lb();this.tipsView.fontFamily="MYingHei_18030-Medium";this.tipsView.color=tb./*<@3>*/tipsColor;this.tipsView.align=lb.Align.Center;this.tipsView.fontSize=UIUtils_1.UIUtils.getPixel(tb.tipsFontSize);this.tipsView.height=UIUtils_1.UIUtils.getPixel(tb.tipsHeight);this.addChild(this.tipsView);this.layout=new ob();this.layout.setLayoutParam(0, "align", /*<@36>*/{left:"parent",right:"parent",middle:"parent"});this.layout.setLayoutParam(1, "align", /*<@38>*/{top:/*<@37>*/{side:"bottom",target:0},left:"parent",right:"parent"});this.layout.setLayoutParam(1, "margin", /*<@35>*/{top:UIUtils_1.UIUtils.getPixel(tb.tipsTopMargin)});this.visibility=mb.Visibility.None;this.on("propertychange", /*<@39>*/(/*<@40>*/tc, /*<@40>*/uc, /*<@1>*/vc) =>{ub.D(`propertychange, property is ${tc}, ${uc}, ${vc}`);if(tc==="width") {this.changeStyleByMode(vc>UIUtils_1.UIUtils.getPixel(tb.floatModeWidth));}});}/*<@24>*/setErrorButtonStyle(){this.errorButton.multiState=/*<@42>*/{"normal":/*<@41>*/{"color":tb./*<@3>*/errorButtonColor,"background":tb./*<@3>*/errorButtonBackground},"pressed":/*<@41>*/{"color":tb./*<@3>*/errorButtonColor,"background":tb./*<@3>*/errorButtonBackground}};}/*<@25>*/changeStyleByMode(/*<@2>*/tc){this.tipsView.visibility=tc ? mb.Visibility.Visible : mb.Visibility.None;this.errorDetailText.visibility=tc ? mb.Visibility.Visible : mb.Visibility.None;if(tc) {this.errorText.height=UIUtils_1.UIUtils.getPixel(tb.errorTextHeight);this.errorText.width=UIUtils_1.UIUtils.getPixel(tb.errorTextWidth);this.infoBox.layout.setLayoutParam(3, "align", /*<@34>*/{top:/*<@33>*/{target:2,side:"bottom"},center:"parent"});}else {this.errorText.height=UIUtils_1.UIUtils.getPixel(tb.errorTextHeightSmall);this.errorText.width=UIUtils_1.UIUtils.getPixel(tb.errorTextWidthSmall);this.errorText.multiLine=true;this.errorText.lineSpacing=UIUtils_1.UIUtils.getPixel(tb.errorTextLineSpace);this.infoBox.layout.setLayoutParam(3, "align", /*<@34>*/{top:/*<@33>*/{target:1,side:"bottom"},center:"parent"});}let /*<@5>*/uc = 0;uc+=this.imageView.visibility===mb.Visibility.Visible ? (this.imageView.height+UIUtils_1.UIUtils.getPixel(tb.errorTextTopMargin)) : 0;uc+=this.errorDetailText.visibility===mb.Visibility.Visible ? (this.errorDetailText.height+UIUtils_1.UIUtils.getPixel(tb.errorDetailTextTopMargin)) : 0;uc+=(this.errorText.height+this.errorButton.height+UIUtils_1.UIUtils.getPixel(tb.errorButtonTopMargin));this.infoBox.height=uc;}/*<@26>*/show(/*<@2>*/tc, /*<@27>*/uc, /*<@27>*/vc){this.visibility=mb.Visibility.Visible;if(tc) {this.imageView.visibility=mb.Visibility.Visible;this.infoBox.layout.setLayoutParam(1, "margin", /*<@35>*/{top:UIUtils_1.UIUtils.getPixel(tb.errorTextTopMargin)});}else {this.imageView.visibility=mb.Visibility.None;this.infoBox.layout.setLayoutParam(1, "margin", /*<@43>*/{top:0});}this.changeStyleByMode(this.width>UIUtils_1.UIUtils.getPixel(tb.errorInfoMinWidth));this.errorButton.addGestureRecognizer(new rb());this.errorButton.removeAllEventListeners("tap");this.errorButton.on("tap", /*<@44>*/(/*<@45>*/wc) =>{wc.srcEvent.stopPropagation();if(this.prevTapTime+1000<Date.now()) {ub.D(`retry button clicked!`);this.prevTapTime=Date.now();uc();this.clearTipsChangeTimer();this.visibility=mb.Visibility.Hidden;}});this.removeAllEventListeners("touchend");this.on("touchend", /*<@46>*/() =>{ub.D(`videoLimit view touchend!`);if(vc) vc();});this.clearTipsChangeTimer();this.timeIndex=10;this.tipsView.text=this.getTipsText(this.timeIndex);this.tipsChangeTimer=setInterval(/*<@47>*/() =>{this.timeIndex--;if(this.timeIndex===0) {this.clearTipsChangeTimer();if(vc) vc();return;}this.tipsView.text=this.getTipsText(this.timeIndex);}, 1000);}/*<@28>*/hide(){this.clearTipsChangeTimer();this.visibility=mb.Visibility.None;}/*<@29>*/getTipsText(/*<@1>*/tc){return qb.getInstance().getString("EXIT_VIDEO_TIPS", /*<@48>*/{"time":String(tc)});}/*<@30>*/clearTipsChangeTimer(){if(this.tipsChangeTimer!==null) {clearInterval(this.tipsChangeTimer);this.tipsChangeTimer=null;}}/*<@31>*/destroy(){this.clearTipsChangeTimer();this.removeAllListeners("propertychange");super.destroy(true);}};
